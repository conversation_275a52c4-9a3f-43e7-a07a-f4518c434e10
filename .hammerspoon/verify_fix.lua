-- 验证修复是否成功

print("=== Verifying Cron Task Fix ===")

-- 1. 检查文件路径是否正确
local cronTaskFile = hs.configdir .. "/Spoons/TaskList.spoon/CronTask.yml"
print("1. Checking file path: " .. cronTaskFile)

local file = io.open(cronTaskFile, "r")
if file then
    print("   ✓ File accessible")
    file:close()
else
    print("   ✗ File not accessible")
    return
end

-- 2. 检查当前日期
local now = os.time()
local dayOfMonth = tonumber(os.date("%d", now))
local weekday = tonumber(os.date("%w", now))
local weekOfYear = tonumber(os.date("%W", now))

print("2. Current date info:")
print("   Date: " .. os.date("%Y-%m-%d", now))
print("   Day: " .. dayOfMonth .. " (2daily should " .. (dayOfMonth % 2 == 0 and "show" or "hide") .. ")")
print("   Weekday: " .. weekday .. " (weekly should " .. (weekday == 6 and "show" or "hide") .. ")")
print("   Week: " .. weekOfYear .. " (2weekly should " .. (weekday == 6 and weekOfYear % 2 == 0 and "show" or "hide") .. ")")

-- 3. 手动测试YAML解析
print("3. Testing YAML parsing...")

-- 简化的YAML解析测试
local function testParseYAML()
    local file = io.open(cronTaskFile, "r")
    if not file then return {} end
    
    local content = file:read("*all")
    file:close()
    
    local result = {}
    local lines = {}
    
    for line in content:gmatch("[^\r\n]+") do
        table.insert(lines, line)
    end
    
    local currentItem = nil
    local i = 1
    
    while i <= #lines do
        local line = lines[i]
        
        if line:match("^%s*$") or line:match("^%s*#") or line:match("^%s*---") then
            i = i + 1
        elseif line:match("^%s*-%s*type:%s*(.+)") then
            local type = line:match("^%s*-%s*type:%s*(.+)")
            currentItem = {
                type = type:gsub("%s+$", ""),
                item = {}
            }
            table.insert(result, currentItem)
            print("   Found type: " .. currentItem.type)
            i = i + 1
        elseif line:match("^%s*item:%s*$") then
            i = i + 1
        elseif line:match("^%s*-%s*task:%s*(.+)") and currentItem then
            local task = line:match("^%s*-%s*task:%s*(.+)")
            task = task:gsub("%s*#.*$", ""):gsub("%s+$", ""):gsub('^"', ""):gsub('"$', "")
            if task ~= "" then
                table.insert(currentItem.item, {task = task})
                print("     Task: " .. task)
            end
            i = i + 1
        else
            i = i + 1
        end
    end
    
    return result
end

local cronTasks = testParseYAML()
print("   Parsed " .. #cronTasks .. " task types")

-- 4. 测试过滤逻辑
print("4. Testing filter logic...")

local function testShouldShow(taskType)
    local isMatched = taskType:match("^(%d+)")
    local number = isMatched and tonumber(isMatched) or 1
    local baseType = taskType:gsub("^%d+", "")
    
    if baseType == "daily" then
        if isMatched then
            return dayOfMonth % number == 0
        else
            return true
        end
    elseif baseType == "weekly" then
        if weekday == 6 then
            if isMatched then
                return weekOfYear % number == 0
            else
                return true
            end
        else
            return false
        end
    else
        return false
    end
end

local expectedTasks = {}
for _, cronTask in ipairs(cronTasks) do
    local shouldShow = testShouldShow(cronTask.type)
    print("   " .. cronTask.type .. ": " .. (shouldShow and "SHOW" or "hide") .. " (" .. #cronTask.item .. " items)")
    
    if shouldShow then
        for _, item in ipairs(cronTask.item) do
            table.insert(expectedTasks, "@" .. cronTask.type .. " " .. item.task)
        end
    end
end

print("5. Expected tasks to be added:")
for i, task in ipairs(expectedTasks) do
    print("   " .. i .. ". " .. task)
end

print("\n=== Summary ===")
print("Expected " .. #expectedTasks .. " cron tasks to be added to TaskList")
print("Check the TaskList menu to verify they appear")

-- 发送通知
hs.notify.new({
    title = "Cron Fix Verification",
    informativeText = "Expected " .. #expectedTasks .. " tasks. Check TaskList menu.",
    withdrawAfter = 8
}):send()
