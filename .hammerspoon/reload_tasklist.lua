-- 重新加载TaskList来测试cron任务功能

-- 停止现有的TaskList
if spoon.TaskList then
    spoon.TaskList:stop()
end

-- 重新加载TaskList
hs.spoons.use("TaskList", {
    start = true
})

-- 发送通知
hs.notify.new({
    title = "TaskList Reloaded",
    informativeText = "Check console for cron task loading logs",
    withdrawAfter = 5
}):send()

print("TaskList reloaded. Check the Hammerspoon console for cron task loading logs.")
