-- 测试修复后的cron任务功能

print("=== Testing Fixed Cron Task Implementation ===")

-- 检查文件路径
local cronTaskFile = hs.configdir .. "/Spoons/TaskList.spoon/CronTask.yml"
print("Checking file: " .. cronTaskFile)

local file = io.open(cronTaskFile, "r")
if file then
    print("✓ File found and accessible")
    local content = file:read("*all")
    file:close()
    print("File size: " .. #content .. " characters")
    
    -- 显示前几行来验证内容
    local lineCount = 0
    for line in content:gmatch("[^\r\n]+") do
        lineCount = lineCount + 1
        if lineCount <= 10 then
            print("Line " .. lineCount .. ": " .. line)
        end
    end
    print("Total lines: " .. lineCount)
else
    print("✗ File not found or not accessible")
end

-- 显示当前日期信息
print("\n=== Current Date Info ===")
local now = os.time()
print("Date: " .. os.date("%Y-%m-%d", now))
print("Day of month: " .. os.date("%d", now))
print("Weekday: " .. os.date("%w", now) .. " (0=Sunday, 6=Saturday)")
print("Week of year: " .. os.date("%W", now))

-- 计算预期结果
local dayOfMonth = tonumber(os.date("%d", now))
local weekday = tonumber(os.date("%w", now))
local weekOfYear = tonumber(os.date("%W", now))

print("\n=== Expected Task Types to Show ===")
print("daily: ✓ (always)")
print("2daily: " .. (dayOfMonth % 2 == 0 and "✓" or "✗") .. " (day " .. dayOfMonth .. " % 2 = " .. (dayOfMonth % 2) .. ")")
print("weekly: " .. (weekday == 6 and "✓" or "✗") .. " (weekday " .. weekday .. ", need 6)")
print("2weekly: " .. (weekday == 6 and weekOfYear % 2 == 0 and "✓" or "✗") .. " (Saturday and week " .. weekOfYear .. " % 2 = " .. (weekOfYear % 2) .. ")")
print("4weekly: " .. (weekday == 6 and weekOfYear % 4 == 0 and "✓" or "✗") .. " (Saturday and week " .. weekOfYear .. " % 4 = " .. (weekOfYear % 4) .. ")")

print("\n=== Restarting TaskList to Test ===")

-- 重启TaskList来触发cron任务加载
if spoon.TaskList then
    print("Stopping TaskList...")
    spoon.TaskList:stop()
    
    hs.timer.doAfter(1, function()
        print("Starting TaskList...")
        spoon.TaskList:start()
        
        hs.timer.doAfter(2, function()
            print("TaskList restarted. Check Hammerspoon console for detailed logs.")
            print("Also check the TaskList menu for cron tasks with @daily, @2daily, @weekly prefixes.")
            
            hs.notify.new({
                title = "Cron Task Test",
                informativeText = "TaskList restarted. Check console logs and menu.",
                withdrawAfter = 8
            }):send()
        end)
    end)
else
    print("TaskList spoon not found")
end
