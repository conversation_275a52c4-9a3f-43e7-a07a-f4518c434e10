-- 测试重构后的TaskList

print("=== Testing Refactored TaskList ===")

-- 1. 测试模块加载
print("1. Testing module loading...")
local spoonPath = hs.configdir .. "/Spoons/TaskList.spoon"

local success1, cronTasks = pcall(dofile, spoonPath .. "/cron_tasks.lua")
local success2, taskManager = pcall(dofile, spoonPath .. "/task_manager.lua")
local success3, dataManager = pcall(dofile, spoonPath .. "/data_manager.lua")

print("   cron_tasks.lua: " .. (success1 and "✓" or "✗"))
print("   task_manager.lua: " .. (success2 and "✓" or "✗"))
print("   data_manager.lua: " .. (success3 and "✓" or "✗"))

if not (success1 and success2 and success3) then
    print("Module loading failed, stopping test")
    return
end

-- 2. 测试CronTask文件访问
print("2. Testing CronTask file access...")
local cronTaskFile = hs.configdir .. "/Spoons/TaskList.spoon/CronTask.yml"
local file = io.open(cronTaskFile, "r")
if file then
    print("   ✓ CronTask.yml accessible")
    file:close()
else
    print("   ✗ CronTask.yml not accessible")
end

-- 3. 测试cron任务加载
print("3. Testing cron task loading...")
local logger = hs.logger.new('Test')
local cronTaskList = cronTasks.loadCronTasks(logger)
print("   Loaded " .. #cronTaskList .. " cron tasks")

for i, task in ipairs(cronTaskList) do
    print("   " .. i .. ". " .. task.type .. " - " .. task.task)
end

-- 4. 显示当前日期信息
print("4. Current date info:")
local now = os.time()
local dayOfMonth = tonumber(os.date("%d", now))
local weekday = tonumber(os.date("%w", now))
local weekOfYear = tonumber(os.date("%W", now))

print("   Date: " .. os.date("%Y-%m-%d", now))
print("   Day: " .. dayOfMonth .. " (2daily: " .. (dayOfMonth % 2 == 0 and "show" or "hide") .. ")")
print("   Weekday: " .. weekday .. " (weekly: " .. (weekday == 6 and "show" or "hide") .. ")")
print("   Week: " .. weekOfYear .. " (2weekly: " .. (weekday == 6 and weekOfYear % 2 == 0 and "show" or "hide") .. ")")

-- 5. 重启TaskList
print("5. Restarting TaskList...")

if spoon.TaskList then
    print("   Stopping existing TaskList...")
    spoon.TaskList:stop()
    
    hs.timer.doAfter(1, function()
        print("   Starting TaskList...")
        local success, err = pcall(function()
            spoon.TaskList:start()
        end)
        
        if success then
            print("   ✓ TaskList started successfully")
            
            hs.timer.doAfter(2, function()
                print("   TaskList should now have cron tasks loaded")
                print("   Check the TaskList menu for tasks with @daily, @2daily, @weekly prefixes")
                
                hs.notify.new({
                    title = "TaskList Test Complete",
                    informativeText = "Expected " .. #cronTaskList .. " cron tasks. Check menu.",
                    withdrawAfter = 8
                }):send()
            end)
        else
            print("   ✗ TaskList start failed: " .. tostring(err))
        end
    end)
else
    print("   ✗ TaskList spoon not found")
end
