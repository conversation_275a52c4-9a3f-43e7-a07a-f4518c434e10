-- 简单的cron任务测试

print("=== Cron Task Test ===")

-- 显示当前日期信息
local now = os.time()
print("Current date: " .. os.date("%Y-%m-%d", now))
print("Day of month: " .. os.date("%d", now))
print("Weekday: " .. os.date("%w", now) .. " (0=Sunday, 6=Saturday)")
print("Week of year: " .. os.date("%W", now))
print("")

-- 测试应该显示的任务类型
local dayOfMonth = tonumber(os.date("%d", now))
local weekday = tonumber(os.date("%w", now))
local weekOfYear = tonumber(os.date("%W", now))

print("Expected tasks for today:")

-- daily 任务总是显示
print("✓ daily tasks (always show)")

-- 2daily: 26 % 2 = 0，应该显示
if dayOfMonth % 2 == 0 then
    print("✓ 2daily tasks (day " .. dayOfMonth .. " % 2 = 0)")
else
    print("✗ 2daily tasks (day " .. dayOfMonth .. " % 2 = " .. (dayOfMonth % 2) .. ")")
end

-- weekly: 今天是星期六，应该显示
if weekday == 6 then
    print("✓ weekly tasks (Saturday)")
else
    print("✗ weekly tasks (not Saturday, weekday = " .. weekday .. ")")
end

-- 2weekly: 今天是星期六且第29周，29 % 2 = 1，不应该显示
if weekday == 6 and weekOfYear % 2 == 0 then
    print("✓ 2weekly tasks (Saturday and week " .. weekOfYear .. " % 2 = 0)")
else
    print("✗ 2weekly tasks (not Saturday or week " .. weekOfYear .. " % 2 = " .. (weekOfYear % 2) .. ")")
end

-- 4weekly: 今天是星期六且第29周，29 % 4 = 1，不应该显示
if weekday == 6 and weekOfYear % 4 == 0 then
    print("✓ 4weekly tasks (Saturday and week " .. weekOfYear .. " % 4 = 0)")
else
    print("✗ 4weekly tasks (not Saturday or week " .. weekOfYear .. " % 4 = " .. (weekOfYear % 4) .. ")")
end

print("")

-- 尝试读取CronTask.yml文件
local cronTaskFile = hs.spoons.resourcePath("TaskList") .. "/CronTask.yml"
print("Trying to read: " .. cronTaskFile)

local file = io.open(cronTaskFile, "r")
if file then
    local content = file:read("*all")
    file:close()
    print("✓ File read successfully, length: " .. #content .. " characters")
    
    -- 显示前几行
    local lines = {}
    for line in content:gmatch("[^\r\n]+") do
        table.insert(lines, line)
        if #lines >= 10 then break end
    end
    
    print("First 10 lines:")
    for i, line in ipairs(lines) do
        print("  " .. i .. ": " .. line)
    end
else
    print("✗ Failed to read file")
end

print("")
print("Test completed. Check TaskList menu to see if cron tasks were added.")

-- 发送通知
hs.notify.new({
    title = "Cron Test",
    informativeText = "Test completed, check console output",
    withdrawAfter = 3
}):send()
