-- 测试cron任务集成

-- 加载TaskList spoon
local taskList = hs.loadSpoon("TaskList")

-- 测试函数
local function testCronIntegration()
    print("=== Testing Cron Task Integration ===")
    
    -- 获取当前日期信息
    local now = os.time()
    local dayOfMonth = tonumber(os.date("%d", now))
    local weekday = tonumber(os.date("%w", now))
    local weekOfYear = tonumber(os.date("%W", now))
    
    print("Current date: " .. os.date("%Y-%m-%d"))
    print("Day of month: " .. dayOfMonth)
    print("Weekday: " .. weekday .. " (0=Sunday, 6=Saturday)")
    print("Week of year: " .. weekOfYear)
    print("")
    
    -- 预期的任务类型
    local expectedTasks = {}
    
    -- daily 任务总是显示
    table.insert(expectedTasks, "daily")
    
    -- 2daily: 26 % 2 = 0，应该显示
    if dayOfMonth % 2 == 0 then
        table.insert(expectedTasks, "2daily")
    end
    
    -- weekly: 今天是星期六，应该显示
    if weekday == 6 then
        table.insert(expectedTasks, "weekly")
    end
    
    -- 2weekly: 今天是星期六且第29周，29 % 2 = 1，不应该显示
    if weekday == 6 and weekOfYear % 2 == 0 then
        table.insert(expectedTasks, "2weekly")
    end
    
    -- 4weekly: 今天是星期六且第29周，29 % 4 = 1，不应该显示
    if weekday == 6 and weekOfYear % 4 == 0 then
        table.insert(expectedTasks, "4weekly")
    end
    
    print("Expected task types to show today:")
    for _, taskType in ipairs(expectedTasks) do
        print("  - " .. taskType)
    end
    print("")
    
    -- 尝试重新加载TaskList来触发cron任务加载
    if taskList then
        print("TaskList spoon loaded successfully")
        -- 这里我们不能直接访问内部函数，但可以通过重启来测试
        print("Please restart TaskList to see cron tasks loaded")
    else
        print("Failed to load TaskList spoon")
    end
end

-- 运行测试
testCronIntegration()

-- 发送通知
hs.notify.new({
    title = "Cron Task Test",
    informativeText = "Check console for test results",
    withdrawAfter = 5
}):send()
