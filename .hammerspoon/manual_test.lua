-- 手动测试cron任务功能

print("=== Manual Cron Task Test ===")

-- 重新启动TaskList来触发cron任务加载
if spoon.TaskList then
    print("Stopping existing TaskList...")
    spoon.TaskList:stop()
    
    -- 等待一秒后重新启动
    hs.timer.doAfter(1, function()
        print("Starting TaskList...")
        spoon.TaskList:start()
        
        -- 再等待2秒后检查结果
        hs.timer.doAfter(2, function()
            print("TaskList restarted. Check the menu for cron tasks.")
            
            -- 发送通知
            hs.notify.new({
                title = "TaskList Test",
                informativeText = "TaskList restarted, check menu for cron tasks",
                withdrawAfter = 5
            }):send()
        end)
    end)
else
    print("TaskList spoon not found")
end
