-- 检查路径和文件访问

print("=== Path Check ===")

-- 检查spoon路径
local spoonPath = hs.spoons.resourcePath("TaskList")
print("Spoon path: " .. (spoonPath or "nil"))

-- 检查CronTask.yml路径
local cronTaskFile = spoonPath .. "/CronTask.yml"
print("CronTask.yml path: " .. cronTaskFile)

-- 检查文件是否存在
local file = io.open(cronTaskFile, "r")
if file then
    print("✓ File exists and can be opened")
    local content = file:read("*all")
    file:close()
    print("File size: " .. #content .. " characters")
    
    -- 显示前几行
    local lineCount = 0
    for line in content:gmatch("[^\r\n]+") do
        lineCount = lineCount + 1
        if lineCount <= 5 then
            print("Line " .. lineCount .. ": " .. line)
        end
    end
    print("Total lines: " .. lineCount)
else
    print("✗ File cannot be opened")
end

-- 检查当前日期
print("\n=== Date Check ===")
local now = os.time()
print("Date: " .. os.date("%Y-%m-%d", now))
print("Day: " .. os.date("%d", now))
print("Weekday: " .. os.date("%w", now) .. " (6=Saturday)")
print("Week: " .. os.date("%W", now))

-- 计算预期结果
local dayOfMonth = tonumber(os.date("%d", now))
local weekday = tonumber(os.date("%w", now))
local weekOfYear = tonumber(os.date("%W", now))

print("\n=== Expected Results ===")
print("daily: always true")
print("2daily: " .. tostring(dayOfMonth % 2 == 0) .. " (day " .. dayOfMonth .. " % 2 = " .. (dayOfMonth % 2) .. ")")
print("weekly: " .. tostring(weekday == 6) .. " (weekday = " .. weekday .. ")")
print("2weekly: " .. tostring(weekday == 6 and weekOfYear % 2 == 0) .. " (Saturday and week " .. weekOfYear .. " % 2 = " .. (weekOfYear % 2) .. ")")
print("4weekly: " .. tostring(weekday == 6 and weekOfYear % 4 == 0) .. " (Saturday and week " .. weekOfYear .. " % 4 = " .. (weekOfYear % 4) .. ")")

print("\nTest completed.")

-- 发送通知总结
local summary = "Path check completed. "
local file = io.open((hs.spoons.resourcePath("TaskList") or "") .. "/CronTask.yml", "r")
if file then
    file:close()
    summary = summary .. "File found. "
else
    summary = summary .. "File NOT found. "
end

local dayOfMonth = tonumber(os.date("%d"))
local weekday = tonumber(os.date("%w"))
summary = summary .. "Today: " .. (dayOfMonth % 2 == 0 and "2daily" or "no 2daily") .. ", " .. (weekday == 6 and "weekly" or "no weekly")

hs.notify.new({
    title = "Path Check Result",
    informativeText = summary,
    withdrawAfter = 10
}):send()
