-- 最终验证脚本

print("=== Final Verification ===")

-- 检查所有文件是否存在
local files = {
    ".hammerspoon/Spoons/TaskList.spoon/init.lua",
    ".hammerspoon/Spoons/TaskList.spoon/cron_tasks.lua", 
    ".hammerspoon/Spoons/TaskList.spoon/task_manager.lua",
    ".hammerspoon/Spoons/TaskList.spoon/data_manager.lua",
    ".hammerspoon/Spoons/TaskList.spoon/CronTask.yml"
}

print("1. File existence check:")
for _, file in ipairs(files) do
    local fullPath = hs.configdir .. "/" .. file:gsub("^%.hammerspoon/", "")
    local f = io.open(fullPath, "r")
    if f then
        f:close()
        print("   ✓ " .. file)
    else
        print("   ✗ " .. file .. " (missing)")
    end
end

-- 检查当前日期和预期的cron任务
print("\n2. Expected cron tasks for today:")
local now = os.time()
local dayOfMonth = tonumber(os.date("%d", now))
local weekday = tonumber(os.date("%w", now))
local weekOfYear = tonumber(os.date("%W", now))

print("   Date: " .. os.date("%Y-%m-%d", now))
print("   Expected task types:")
print("     - daily: ✓ (always show)")
print("     - 2daily: " .. (dayOfMonth % 2 == 0 and "✓" or "✗") .. " (day " .. dayOfMonth .. " % 2 = " .. (dayOfMonth % 2) .. ")")
print("     - weekly: " .. (weekday == 6 and "✓" or "✗") .. " (weekday " .. weekday .. ", need 6 for Saturday)")
print("     - 2weekly: " .. (weekday == 6 and weekOfYear % 2 == 0 and "✓" or "✗") .. " (Saturday and week " .. weekOfYear .. " % 2 = " .. (weekOfYear % 2) .. ")")

-- 计算预期的任务数量
local expectedTypes = {"daily"}
if dayOfMonth % 2 == 0 then table.insert(expectedTypes, "2daily") end
if weekday == 6 then table.insert(expectedTypes, "weekly") end
if weekday == 6 and weekOfYear % 2 == 0 then table.insert(expectedTypes, "2weekly") end
if weekday == 6 and weekOfYear % 4 == 0 then table.insert(expectedTypes, "4weekly") end

print("\n3. Summary:")
print("   Expected task types to show: " .. table.concat(expectedTypes, ", "))
print("   Check TaskList menu for tasks with these prefixes: @daily, @2daily, @weekly, etc.")

print("\n=== Verification Complete ===")
print("The TaskList has been refactored and cron task integration should be working.")
print("Check the TaskList menubar item to see if cron tasks appear with @ prefixes.")

hs.notify.new({
    title = "TaskList Ready",
    informativeText = "Refactored with cron tasks. Expected: " .. #expectedTypes .. " types",
    withdrawAfter = 5
}):send()
