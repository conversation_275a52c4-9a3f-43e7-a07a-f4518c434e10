-- 调试cron任务解析

-- 直接测试YAML解析和过滤逻辑
local function parseYAML(content)
    local result = {}
    local lines = {}
    
    -- 分割行
    for line in content:gmatch("[^\r\n]+") do
        table.insert(lines, line)
    end
    
    local currentItem = nil
    local i = 1
    
    while i <= #lines do
        local line = lines[i]
        
        -- 跳过空行和注释
        if line:match("^%s*$") or line:match("^%s*#") or line:match("^%s*---") then
            i = i + 1
        -- 匹配 - type: xxx
        elseif line:match("^%s*-%s*type:%s*(.+)") then
            local type = line:match("^%s*-%s*type:%s*(.+)")
            currentItem = {
                type = type:gsub("%s+$", ""), -- 去除尾部空格
                item = {}
            }
            table.insert(result, currentItem)
            i = i + 1
        -- 匹配 item:
        elseif line:match("^%s*item:%s*$") then
            i = i + 1
        -- 匹配 - task: xxx
        elseif line:match("^%s*-%s*task:%s*(.+)") and currentItem then
            local task = line:match("^%s*-%s*task:%s*(.+)")
            -- 去除注释部分
            task = task:gsub("%s*#.*$", "")
            task = task:gsub("%s+$", "") -- 去除尾部空格
            table.insert(currentItem.item, {task = task})
            i = i + 1
        else
            i = i + 1
        end
    end
    
    return result
end

-- 提取时间数字（如 "2daily" -> 2）
local function extractTimeNumber(taskType)
    local number = taskType:match("^(%d+)")
    if number then
        return true, tonumber(number)
    else
        return false, 1
    end
end

-- 判断任务是否应该显示
local function shouldShowTask(taskType)
    local now = os.time()
    local isMatched, number = extractTimeNumber(taskType)
    local baseType = taskType:gsub("^%d+", "") -- 去除数字前缀
    
    print("Testing: " .. taskType .. " -> base: " .. baseType .. ", number: " .. number)
    
    if baseType == "daily" then
        if isMatched then
            local dayOfMonth = tonumber(os.date("%d", now))
            local result = dayOfMonth % number == 0
            print("  Daily check: day " .. dayOfMonth .. " % " .. number .. " = " .. (dayOfMonth % number) .. " -> " .. tostring(result))
            return result
        else
            print("  Daily: always true")
            return true
        end
    elseif baseType == "weekly" then
        local weekday = tonumber(os.date("%w", now))
        print("  Weekly check: weekday = " .. weekday .. " (need 6 for Saturday)")
        if weekday == 6 then
            if isMatched then
                local weekOfYear = tonumber(os.date("%W", now))
                local result = weekOfYear % number == 0
                print("  Weekly number check: week " .. weekOfYear .. " % " .. number .. " = " .. (weekOfYear % number) .. " -> " .. tostring(result))
                return result
            else
                print("  Weekly: Saturday, show")
                return true
            end
        else
            print("  Weekly: not Saturday, hide")
            return false
        end
    else
        print("  Other type: hide")
        return false
    end
end

-- 读取CronTask.yml文件
local cronTaskFile = hs.spoons.resourcePath("TaskList") .. "/CronTask.yml"
print("Reading cron task file: " .. cronTaskFile)

local file = io.open(cronTaskFile, "r")
if file then
    local content = file:read("*all")
    file:close()
    
    print("File content length: " .. #content)
    print("First 200 chars: " .. content:sub(1, 200))
    print("")
    
    local cronTasks = parseYAML(content)
    print("Parsed " .. #cronTasks .. " cron task types:")
    
    for i, cronTask in ipairs(cronTasks) do
        print("Type " .. i .. ": " .. cronTask.type .. " (" .. #cronTask.item .. " items)")
        local shouldShow = shouldShowTask(cronTask.type)
        print("  Should show: " .. tostring(shouldShow))
        
        if shouldShow then
            for j, item in ipairs(cronTask.item) do
                print("    Task: " .. item.task)
            end
        end
        print("")
    end
else
    print("Failed to open file: " .. cronTaskFile)
end

-- 显示当前日期信息
print("=== Current Date Info ===")
print("Date: " .. os.date("%Y-%m-%d"))
print("Day of month: " .. os.date("%d"))
print("Weekday: " .. os.date("%w") .. " (0=Sunday, 6=Saturday)")
print("Week of year: " .. os.date("%W"))
