-- CronTask 相关功能模块

local M = {}

-- 数据持久化文件路径
local cronTaskFile = hs.configdir .. "/Spoons/TaskList.spoon/CronTask.yml"

-- 简单的YAML解析函数（仅支持我们需要的格式）
local function parseYAML(content)
    local result = {}
    local lines = {}
    
    -- 分割行
    for line in content:gmatch("[^\r\n]+") do
        table.insert(lines, line)
    end
    
    local currentItem = nil
    local i = 1
    
    while i <= #lines do
        local line = lines[i]
        
        -- 跳过空行和注释
        if line:match("^%s*$") or line:match("^%s*#") or line:match("^%s*---") then
            i = i + 1
        -- 匹配 - type: xxx
        elseif line:match("^%s*-%s*type:%s*(.+)") then
            local type = line:match("^%s*-%s*type:%s*(.+)")
            currentItem = {
                type = type:gsub("%s+$", ""), -- 去除尾部空格
                item = {}
            }
            table.insert(result, currentItem)
            i = i + 1
        -- 匹配 item:
        elseif line:match("^%s*item:%s*$") then
            i = i + 1
        -- 匹配 - task: xxx
        elseif line:match("^%s*-%s*task:%s*(.+)") and currentItem then
            local task = line:match("^%s*-%s*task:%s*(.+)")
            -- 去除注释部分
            task = task:gsub("%s*#.*$", "")
            task = task:gsub("%s+$", "") -- 去除尾部空格
            -- 去除引号
            task = task:gsub('^"', ""):gsub('"$', "")
            if task ~= "" then
                table.insert(currentItem.item, {task = task})
            end
            i = i + 1
        else
            i = i + 1
        end
    end
    
    return result
end

-- 提取时间数字（如 "2daily" -> 2）
local function extractTimeNumber(taskType)
    local number = taskType:match("^(%d+)")
    if number then
        return true, tonumber(number)
    else
        return false, 1
    end
end

-- 判断任务是否应该显示
local function shouldShowTask(taskType, logger)
    local now = os.time()
    local isMatched, number = extractTimeNumber(taskType)
    local baseType = taskType:gsub("^%d+", "") -- 去除数字前缀
    
    if logger then
        logger.i("Checking task type: " .. taskType .. " -> base: " .. baseType .. ", number: " .. number)
    end
    
    if baseType == "daily" then
        if isMatched then
            -- 对于 2daily 等，按照当前日期的天数计算
            local dayOfMonth = tonumber(os.date("%d", now))
            local result = dayOfMonth % number == 0
            if logger then
                logger.i("Daily check: day " .. dayOfMonth .. " % " .. number .. " = " .. (dayOfMonth % number) .. " -> " .. tostring(result))
            end
            return result
        else
            if logger then
                logger.i("Daily: always true")
            end
            return true -- 普通 daily 任务总是显示
        end
    elseif baseType == "weekly" then
        -- 周六才显示周任务
        local weekday = tonumber(os.date("%w", now)) -- 0=Sunday, 6=Saturday
        if logger then
            logger.i("Weekly check: weekday = " .. weekday .. " (need 6 for Saturday)")
        end
        if weekday == 6 then -- 周六
            if isMatched then
                local weekOfYear = tonumber(os.date("%W", now))
                local result = weekOfYear % number == 0
                if logger then
                    logger.i("Weekly number check: week " .. weekOfYear .. " % " .. number .. " = " .. (weekOfYear % number) .. " -> " .. tostring(result))
                end
                return result
            else
                if logger then
                    logger.i("Weekly: Saturday, show")
                end
                return true
            end
        else
            if logger then
                logger.i("Weekly: not Saturday, hide")
            end
            return false
        end
    elseif baseType == "monthly" then
        -- 每月1号显示月任务
        local dayOfMonth = tonumber(os.date("%d", now))
        if logger then
            logger.i("Monthly check: day of month = " .. dayOfMonth)
        end
        if dayOfMonth == 1 then
            if isMatched then
                local month = tonumber(os.date("%m", now))
                local result = month % number == 0
                if logger then
                    logger.i("Monthly number check: month " .. month .. " % " .. number .. " = " .. (month % number) .. " -> " .. tostring(result))
                end
                return result
            else
                if logger then
                    logger.i("Monthly: first day, show")
                end
                return true
            end
        else
            if logger then
                logger.i("Monthly: not first day, hide")
            end
            return false
        end
    elseif baseType == "yearly" then
        -- 每年1月1日显示年任务
        local month = tonumber(os.date("%m", now))
        local day = tonumber(os.date("%d", now))
        local result = month == 1 and day == 1
        if logger then
            logger.i("Yearly check: month " .. month .. ", day " .. day .. " -> " .. tostring(result))
        end
        return result
    else
        if logger then
            logger.i("Unknown task type: " .. baseType)
        end
        return false
    end
end

-- 过滤cron任务
local function filterCronTasks(cronTasks, logger)
    local filteredTasks = {}
    
    for _, cronTask in ipairs(cronTasks) do
        if shouldShowTask(cronTask.type, logger) then
            for _, item in ipairs(cronTask.item) do
                table.insert(filteredTasks, {
                    type = "@" .. cronTask.type,
                    task = item.task
                })
            end
        end
    end
    
    return filteredTasks
end

-- 加载cron任务
function M.loadCronTasks(logger)
    if logger then
        logger.i("Attempting to load cron tasks from: " .. cronTaskFile)
    end
    
    local file = io.open(cronTaskFile, "r")
    if file then
        local content = file:read("*all")
        file:close()
        
        if logger then
            logger.i("File read successfully, content length: " .. #content)
        end
        
        local cronTasks = parseYAML(content)
        if logger then
            logger.i("Parsed " .. #cronTasks .. " cron task types")
            
            for i, cronTask in ipairs(cronTasks) do
                logger.i("Cron task type " .. i .. ": " .. cronTask.type .. " (" .. #cronTask.item .. " items)")
            end
        end
        
        local filteredTasks = filterCronTasks(cronTasks, logger)
        if logger then
            logger.i("Filtered to " .. #filteredTasks .. " tasks")
            
            for i, task in ipairs(filteredTasks) do
                logger.i("Filtered task " .. i .. ": " .. task.type .. " - " .. task.task)
            end
        end
        
        return filteredTasks
    else
        if logger then
            logger.w("CronTask.yml file not found: " .. cronTaskFile)
        end
        return {}
    end
end

return M
