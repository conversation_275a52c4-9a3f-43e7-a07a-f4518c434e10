-- 数据持久化相关功能模块

local M = {}

-- 数据持久化文件路径
local dataFile = hs.configdir .. "/tasks_data.json"

-- 保存任务数据
function M.saveTasks(tasks, currentTaskId)
    local data = {
        tasks = tasks,
        currentTaskId = currentTaskId
    }
    local file = io.open(dataFile, "w")
    if file then
        file:write(hs.json.encode(data))
        file:close()
        return true
    end
    return false
end

-- 加载任务数据
function M.loadTasks(taskManager, cronTasks, logger)
    local tasks = {}
    local currentTaskId = nil
    
    local file = io.open(dataFile, "r")
    if file then
        local content = file:read("*all")
        file:close()
        local success, data = pcall(hs.json.decode, content)
        if success and data then
            tasks = data.tasks or {}
            -- 兼容旧数据：如果保存的是索引，转换为ID
            if data.currentTaskIndex and type(data.currentTaskIndex) == "number" and tasks[data.currentTaskIndex] then
                currentTaskId = tasks[data.currentTaskIndex].id
            else
                currentTaskId = data.currentTaskId
            end
            -- 兼容旧数据，为没有新字段的任务添加默认值
            for i, task in ipairs(tasks) do
                if type(task) == "string" then
                    local defaultDate = taskManager.getCurrentDate()
                    local addTime = math.floor(hs.timer.secondsSinceEpoch() * 1000) -- 为旧任务生成添加时间
                    tasks[i] = {
                        id = taskManager.generateTaskId(addTime, task, defaultDate, 1),
                        name = task,
                        date = defaultDate,
                        addTime = addTime,
                        estimatedTime = 1, -- 默认1个E1f
                        actualTime = 0,
                        isDone = false,
                        doneAt = nil,
                        startTime = nil
                    }
                else
                    -- 为旧任务添加 addTime 字段
                    task.addTime = task.addTime or math.floor(hs.timer.secondsSinceEpoch() * 1000)
                    task.id = task.id or taskManager.generateTaskId(task.addTime, task.name or "unknown", task.date or taskManager.getCurrentDate(), task.estimatedTime or 1)
                    task.date = task.date or taskManager.getCurrentDate()
                    task.estimatedTime = task.estimatedTime or 1
                    task.actualTime = task.actualTime or 0
                    task.isDone = task.isDone or false
                    task.doneAt = task.doneAt or task.deletedAt or nil
                    task.startTime = task.startTime or nil
                end
            end
        end
    end
    
    -- 加载完常规任务后，添加cron任务
    local cronTaskList = cronTasks.loadCronTasks(logger)
    local needSave = taskManager.addCronTasksToList(tasks, cronTaskList, logger)
    
    return tasks, currentTaskId, needSave
end

return M
