-- 测试cron任务解析和过滤逻辑

-- 简单的YAML解析函数（仅支持我们需要的格式）
local function parseYAML(content)
    local result = {}
    local lines = {}
    
    -- 分割行
    for line in content:gmatch("[^\r\n]+") do
        table.insert(lines, line)
    end
    
    local currentItem = nil
    local i = 1
    
    while i <= #lines do
        local line = lines[i]
        
        -- 跳过空行和注释
        if line:match("^%s*$") or line:match("^%s*#") or line:match("^%s*---") then
            i = i + 1
        -- 匹配 - type: xxx
        elseif line:match("^%s*-%s*type:%s*(.+)") then
            local type = line:match("^%s*-%s*type:%s*(.+)")
            currentItem = {
                type = type:gsub("%s+$", ""), -- 去除尾部空格
                item = {}
            }
            table.insert(result, currentItem)
            i = i + 1
        -- 匹配 item:
        elseif line:match("^%s*item:%s*$") then
            i = i + 1
        -- 匹配 - task: xxx
        elseif line:match("^%s*-%s*task:%s*(.+)") and currentItem then
            local task = line:match("^%s*-%s*task:%s*(.+)")
            -- 去除注释部分
            task = task:gsub("%s*#.*$", "")
            task = task:gsub("%s+$", "") -- 去除尾部空格
            table.insert(currentItem.item, {task = task})
            i = i + 1
        else
            i = i + 1
        end
    end
    
    return result
end

-- 提取时间数字（如 "2daily" -> 2）
local function extractTimeNumber(taskType)
    local number = taskType:match("^(%d+)")
    if number then
        return true, tonumber(number)
    else
        return false, 1
    end
end

-- 判断任务是否应该显示
local function shouldShowTask(taskType)
    local now = os.time()
    local isMatched, number = extractTimeNumber(taskType)
    local baseType = taskType:gsub("^%d+", "") -- 去除数字前缀
    
    print("Testing task type: " .. taskType .. ", base: " .. baseType .. ", number: " .. number)
    
    if baseType == "daily" then
        if isMatched then
            -- 对于 2daily 等，按照当前日期的天数计算
            local dayOfMonth = tonumber(os.date("%d", now))
            print("Day of month: " .. dayOfMonth .. ", should show: " .. tostring(dayOfMonth % number == 0))
            return dayOfMonth % number == 0
        else
            return true -- 普通 daily 任务总是显示
        end
    elseif baseType == "weekly" then
        -- 周六才显示周任务
        local weekday = tonumber(os.date("%w", now)) -- 0=Sunday, 6=Saturday
        print("Weekday: " .. weekday .. " (6=Saturday)")
        if weekday == 6 then -- 周六
            if isMatched then
                local weekOfYear = tonumber(os.date("%W", now))
                print("Week of year: " .. weekOfYear .. ", should show: " .. tostring(weekOfYear % number == 0))
                return weekOfYear % number == 0
            else
                return true
            end
        else
            return false
        end
    elseif baseType == "monthly" then
        -- 每月1号显示月任务
        local dayOfMonth = tonumber(os.date("%d", now))
        print("Day of month: " .. dayOfMonth)
        if dayOfMonth == 1 then
            if isMatched then
                local month = tonumber(os.date("%m", now))
                print("Month: " .. month .. ", should show: " .. tostring(month % number == 0))
                return month % number == 0
            else
                return true
            end
        else
            return false
        end
    elseif baseType == "yearly" then
        -- 每年1月1日显示年任务
        local month = tonumber(os.date("%m", now))
        local day = tonumber(os.date("%d", now))
        print("Month: " .. month .. ", Day: " .. day)
        return month == 1 and day == 1
    else
        return false
    end
end

-- 测试
print("Current date: " .. os.date("%Y-%m-%d"))
print("Current day of month: " .. os.date("%d"))
print("Current weekday: " .. os.date("%w") .. " (0=Sunday, 6=Saturday)")
print("Current week of year: " .. os.date("%W"))
print("")

-- 测试不同类型的任务
local testTypes = {"daily", "2daily", "weekly", "2weekly", "4weekly", "monthly", "yearly"}

for _, taskType in ipairs(testTypes) do
    print("=== Testing " .. taskType .. " ===")
    local shouldShow = shouldShowTask(taskType)
    print("Should show: " .. tostring(shouldShow))
    print("")
end

-- 测试YAML解析
print("=== Testing YAML parsing ===")
local yamlContent = [[
---

- type: daily
  item:
    - task: 每天刮胡子
    - task: 【每天早上晨跑】每天俯卧撑+HIIT+拉伸

- type: 2daily
  item:
    - task: 换袜子 + 内裤

- type: weekly
  item:
    - task: 每周清洁工作
    - task: 【每周复盘】写周报

- type: 2weekly
  item:
    - task: 刷jav
    - task: 打飞机，晚上洗澡的时候顺便
]]

local parsed = parseYAML(yamlContent)
for i, cronTask in ipairs(parsed) do
    print("Type: " .. cronTask.type)
    for j, item in ipairs(cronTask.item) do
        print("  Task: " .. item.task)
    end
    print("")
end
