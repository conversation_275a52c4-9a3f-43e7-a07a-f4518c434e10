---


- type: daily
  item:
    - task: 每天刮胡子
    - task: 【每天早上晨跑】每天俯卧撑+HIIT+拉伸

    - task: 【每天提前30分钟到岗】也就是8:30到达公司 # 那就需要7:30起床，不赖床。
    - task: 【独立开发者】“一个成熟的独立开发者每天都应该毙掉至少三个想法” 每天用精益画布综合分析3个项目，培养对产品的感觉
    - task: 【每日复盘】每天通过跟LLM模型用英文语音聊天，做每日复盘


#      item:
#        - "***每日复盘：复盘一般指的是复盘某个已经完成的事情，这些task通常需要持续至少几天。但是当我们使用PDCA对某段时间进行复盘时，其实需要对普通的PDCA流程进行一些调整。怎么（用PDCA）做“每日复盘”？每天晚上 review 流程？***" # 1、晚上睡觉前 review 下白天写过的代码，说不定会有新发现。先自己养成每天 CR 当天代码的习惯。2、*今日事今日毕，每天晚上睡前 15～30 分钟思考几个重要问题*，比如思考当前要解决的问题，或者有哪些新工具和方法可以整合进自己的体系等等，然后就去睡觉。3、第二天早上起来，抓住没看手机、没人打扰的这段时间，再接着昨天睡前的问题重新思考，看看有没有什么新想法。给第二天要做的事情的优先级排序。
#        - 为人谋而不忠乎？与朋友交而不信乎？传不习乎？
#        - # 今日已完成、产生问题、解决方案、明日任务。具体来说：1、今日完成了哪些工作。2、遇到了什么问题。3、准备尝试哪些措施。4、明日任务。
#
#          #- task: 泡茶喝茶
#          #- task: 每天晚上早睡
#          #- task: 每天早起 7:00 # 【睡眠】需要养成//保证11-7的睡眠习惯和节奏 # 最近这一年多的睡眠习惯真的很差，一直都是一点，甚至一点半才睡，早上又是8点多甚至9点才起床。
#          #- task: 每天早上golang训练
#
#          #- task: 每天吃4片善存多维复合维生素片
#
#          # **晚上10点-10点半睡觉，早上6点起（晚上7个半到8个小时就足够了** ✅）
#          #
#          #
#          #- **早上一到6点就自动起床了，所以一定要早睡；不然睡眠时间不够。**
#          #- 睡前做一个放松心情的冥想，放空自己，保持好心情，第二天起床的状态会很不一样，很放松又充满活力。
#          #- 睡觉前2h不看什么刺激性的，会调动情绪的的东西。
#
#          # 每天6:30出门
#          #享受跑步✨（跟身体对话）
#          #
#          ## 早起流程
#          #
#          ### 流程
#          #
#          #- 6点起床（~~起床之前做3*20个提肛运动，想想当天要做的事~~ 起床前在床上做10个深呼吸（起床和起立时，动作要慢）；避免"直立性低血压"；对身体损伤很大。）
#          #- 快速清醒（起床之后怎么恢复身体状态？直接进入晨跑状态？）
#          #	- 穿衣服（睡衣/...）
#          #	- **拉开窗帘，打开窗户（让空气进来）**
#          #	- **立即洗漱**（刮胡子）
#          #	- 叠被子（并清扫床上异物）
#          #	- 喝水（温水，500ml左右，小口多次）
#          #	- **在家热身（大概20min，充分热身和拉伸，激活核心）**
#          #- **6点半出门跑步（热身完成再出门）**
#          #	- 跑前先做马克操（20个，动态拉伸髋部）
#          #- 跑步1h左右（**大概7点半到家**）
#          #- 到家
#          #	- 做早饭（先做再放凉）
#          #	- 收汗，顺便处理notify（**拉伸（大概10min，拉伸动作同上）**）
#          #	- 吃早饭（吃复合维生素片）
#          #	- **饭后散步15min**
#          #	- 洗澡
#          #- ~~小憩30min，8点半起床~~
#          #
#          #
#          ### 热身训练（HIIT+拉伸+核心）
#          #
#          #### HIIT（预计30min）
#          #
#          #- 广播体操（时代在召唤）（至少2遍）
#          #- 开合跳（10x10个）（HIIT要先原地跑/高抬腿，把心率带起来（到130以上），之后再HIIT）
#          #- `保加利亚蹲`或者`弓箭步`（`（10x2（L&R））x5个`）：注意双脚站宽一点，后腿与地面60度夹角，前腿膝盖向外打开，大腿内侧发力，双手感受臀大肌发力，后膝要拉到最低
#          #- 活动胯关节（10x10个）：收住核心，双脚与肩同宽，左右跳
#          #- 抓脚蹲起拉伸（2x10个）：蹲下后双手抓住同侧脚，保持背部挺拔，起立（做到最大程度即可），尽量保证腹部下方贴近腹股沟



- type: 2daily
  item:
    - task: 换袜子 + 内裤
      time: "7:00"

- type: weekly
  item:
    - task: 每周清洁工作
      isAuto: true
      sub:
        - task: 个人清洁（洗衣服） # 洗烘一体
        - task: 房间清洁 # 扫地机器人

    - task: 【每周复盘】写周报
      sub:
        - task: 每周清空各种LLM应用的chat history

    - task: 每周进行一次长距离训练
    - task: 家庭会议
    - task: 定投基金
      isAuto: true

- type: 2weekly
  item:
    - task: 刷jav
    - task: 打飞机，晚上洗澡的时候顺便

- type: 4weekly
  item:
    - task: 每个月月底做“每月消费复盘”
    - task: 理发

- type: yearly
  item:
    - task: 洗牙
    - task: 年度复盘
#      item:
#        - "***【年度复盘PAR】极简生活 minimalist（运动、饮食、睡眠）、个人成长 self-improve（新技术、新兴趣、技术工具、证书获得）、工作 work（职位变动、项目完成、社区贡献）、人际关系 rs、个人财务 money（收入变化、储蓄和投资）。其实 个人OKR（又或者说是年度复盘）无非就是这几种，这就像是一个攀登的过程，要训练的无非就那几项，只不过指标和要求越来越高而已***"
    - task: 做体检
