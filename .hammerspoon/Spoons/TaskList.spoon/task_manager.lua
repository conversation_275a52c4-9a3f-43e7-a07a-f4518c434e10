-- 任务管理相关功能模块

local M = {}

-- 简单的字符串hash函数
local function simpleHash(str)
    local hash = 0
    for i = 1, #str do
        hash = (hash * 31 + string.byte(str, i)) % 2147483647
    end
    return hash
end

-- 生成任务ID的函数（hash(添加时间戳 + 任务内容)）
function M.generateTaskId(addTime, taskName, date, estimatedTime)
    local content = tostring(addTime) .. "|" .. taskName .. "|" .. date .. "|" .. tostring(estimatedTime)
    return tostring(simpleHash(content))
end

-- 根据任务ID查找任务
function M.findTaskById(tasks, taskId)
    if not taskId then return nil end
    for i, task in ipairs(tasks) do
        if task.id == taskId then
            return task, i
        end
    end
    return nil, nil
end

-- 获取当前日期字符串
function M.getCurrentDate()
    return os.date("%Y-%m-%d")
end

-- 获取昨天日期
function M.getYesterdayDate()
    return os.date("%Y-%m-%d", os.time() - 24 * 60 * 60)
end

-- 获取本周的日期范围（周一到今天）
function M.getThisWeekRange()
    local today = os.time()
    local todayWeekday = tonumber(os.date("%w", today)) -- 0=Sunday, 1=Monday, ...

    -- 计算本周一的日期
    local mondayOffset = (todayWeekday == 0) and 6 or (todayWeekday - 1)
    local monday = today - mondayOffset * 24 * 60 * 60

    local mondayStr = os.date("%Y-%m-%d", monday)
    local todayStr = os.date("%Y-%m-%d", today)

    -- 计算周数
    local weekNum = tonumber(os.date("%W", today))

    return mondayStr, todayStr, weekNum
end

-- 获取当前时间字符串
function M.getCurrentTime()
    return os.date("%H:%M")
end

-- 验证日期格式
function M.isValidDate(dateStr)
    if not dateStr then return false end
    local year, month, day = dateStr:match("^(%d%d%d%d)-(%d%d)-(%d%d)$")
    if not year then return false end
    year, month, day = tonumber(year), tonumber(month), tonumber(day)
    if not year or not month or not day then return false end
    if month < 1 or month > 12 then return false end
    if day < 1 or day > 31 then return false end
    return true
end

-- 任务排序函数 (按日期)
function M.sortTasks(tasks)
    table.sort(tasks, function(a, b)
        if a.isDone ~= b.isDone then
            return not a.isDone  -- 未完成的任务排在前面
        end
        return a.date < b.date
    end)
end

-- 获取活跃任务（未完成的任务）
function M.getActiveTasks(tasks)
    local activeTasks = {}
    for i, task in ipairs(tasks) do
        if not task.isDone then
            table.insert(activeTasks, {task = task, index = i})
        end
    end
    return activeTasks
end

-- 检查任务是否已存在（基于任务名称和类型前缀）
function M.taskExists(tasks, taskName)
    for _, task in ipairs(tasks) do
        if task.name == taskName and not task.isDone then
            return true
        end
    end
    return false
end

-- 添加cron任务到任务列表
function M.addCronTasksToList(tasks, cronTasks, logger)
    local addedCount = 0
    
    if logger then
        logger.i("Processing " .. #cronTasks .. " filtered cron tasks")
    end
    
    for _, cronTask in ipairs(cronTasks) do
        local taskName = cronTask.type .. " " .. cronTask.task
        
        -- 检查任务是否已存在，避免重复添加
        if not M.taskExists(tasks, taskName) then
            local addTime = math.floor(hs.timer.secondsSinceEpoch() * 1000)
            local newTask = {
                id = M.generateTaskId(addTime, taskName, M.getCurrentDate(), 1),
                name = taskName,
                date = M.getCurrentDate(),
                addTime = addTime,
                estimatedTime = 1, -- cron任务默认1个E1f
                actualTime = 0,
                isDone = false,
                doneAt = nil,
                startTime = nil
            }
            
            table.insert(tasks, newTask)
            addedCount = addedCount + 1
            if logger then
                logger.i("Added cron task: " .. taskName)
            end
        else
            if logger then
                logger.i("Cron task already exists: " .. taskName)
            end
        end
    end
    
    if addedCount > 0 then
        if logger then
            logger.i("Added " .. addedCount .. " new cron tasks")
        end
        M.sortTasks(tasks)
        return true -- 表示有新任务添加，需要保存
    else
        if logger then
            logger.i("No new cron tasks to add")
        end
        return false
    end
end

return M
